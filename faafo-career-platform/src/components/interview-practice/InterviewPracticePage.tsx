'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Play,
  Plus,
  Clock,
  Target,
  TrendingUp,
  Award,
  Calendar,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Pause,
  RotateCcw,
  Eye
} from 'lucide-react';
import { useCSRFToken } from '@/hooks/useCSRFToken';
import InterviewConfigurationWizard, { InterviewSessionConfig } from './InterviewConfigurationWizard';
import InterviewSessionInterface from './InterviewSessionInterface';
import InterviewResultsAndFeedback from './InterviewResultsAndFeedback';
import InterviewResultsDashboard from './InterviewResultsDashboard';

interface InterviewResponse {
  id: string;
  questionId: string;
  responseText: string;
  responseTime: number;
  preparationTime: number;
  aiScore?: number;
  aiAnalysis?: any;
  feedback?: any;
  strengths?: string[];
  improvements?: any[];
  starMethodScore?: number;
  confidenceLevel?: number;
  communicationScore?: number;
  technicalScore?: number;
  userNotes?: string;
  question: {
    questionText: string;
    questionType: string;
    category: string;
    difficulty: string;
    questionOrder: number;
  };
}

interface InterviewSession {
  id: string;
  sessionType: string;
  careerPath?: string;
  experienceLevel?: string;
  companyType?: string;
  status: string;
  totalQuestions: number;
  completedQuestions: number;
  overallScore?: number;
  timeSpent: number;
  createdAt: string;
  lastActiveAt?: string;
  completedAt?: string;
  aiInsights?: any;
  responses: InterviewResponse[];
  progress?: {
    completed: number;
    total: number;
    percentage: number;
  };
}

interface ProgressStats {
  totalSessions: number;
  completedSessions: number;
  averageScore?: number;
  totalPracticeTime: number;
  currentStreak: number;
  improvementRate?: number;
}

export default function InterviewPracticePage() {
  const { data: session, status } = useSession();
  const { csrfFetch, isLoading: csrfLoading } = useCSRFToken();
  const [currentView, setCurrentView] = useState<'dashboard' | 'configuration' | 'session' | 'results' | 'progress'>('dashboard');
  const [sessions, setSessions] = useState<InterviewSession[]>([]);
  const [progressStats, setProgressStats] = useState<ProgressStats | null>(null);
  const [currentSession, setCurrentSession] = useState<InterviewSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (session?.user?.id && !isLoading) {
      loadDashboardData();
    } else if (status === 'unauthenticated') {
      // User is not authenticated, stop loading
      setIsLoading(false);
    }
  }, [session?.user?.id, status]); // Remove session and isLoading from dependencies to prevent loops

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load recent sessions and progress stats in parallel
      const [sessionsResponse, progressResponse] = await Promise.all([
        fetch('/api/interview-practice?limit=5'),
        fetch('/api/interview-practice/progress'),
      ]);

      if (!sessionsResponse.ok || !progressResponse.ok) {
        throw new Error('Failed to load dashboard data');
      }

      const sessionsData = await sessionsResponse.json();
      const progressData = await progressResponse.json();

      if (sessionsData.success) {
        setSessions(sessionsData.data.sessions || []);
      }

      if (progressData.success) {
        setProgressStats(progressData.data.overallProgress || null);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setError('Failed to load interview practice data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartNewSession = () => {
    setCurrentView('configuration');
  };

  const handleConfigurationComplete = async (config: InterviewSessionConfig) => {
    try {
      setIsLoading(true);
      setError(null);

      // Create new interview session with CSRF protection
      const response = await csrfFetch('/api/interview-practice', {
        method: 'POST',
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to create interview session`);
      }

      const data = await response.json();

      if (data.success && data.data) {
        setCurrentSession(data.data);
        setCurrentView('session');
      } else {
        throw new Error(data.error || 'Invalid response from server');
      }
    } catch (error) {
      console.error('Error creating session:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create interview session. Please try again.';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResumeSession = async (sessionId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/interview-practice/${sessionId}`);
      
      if (!response.ok) {
        throw new Error('Failed to load session');
      }

      const data = await response.json();
      
      if (data.success) {
        setCurrentSession(data.data);
        setCurrentView('session');
      } else {
        throw new Error(data.error || 'Failed to load session');
      }
    } catch (error) {
      console.error('Error loading session:', error);
      setError('Failed to load interview session. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSessionComplete = async (sessionId?: string) => {
    if (sessionId) {
      // Load the completed session with results
      try {
        const response = await fetch(`/api/interview-practice/${sessionId}`);
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setCurrentSession(data.data);
            setCurrentView('results');
            return;
          }
        }
      } catch (error) {
        console.error('Error loading session results:', error);
      }
    }

    // Fallback to dashboard
    setCurrentSession(null);
    setCurrentView('dashboard');
    loadDashboardData(); // Refresh dashboard data
  };

  const handleViewSession = async (sessionId: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/interview-practice/${sessionId}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setCurrentSession(data.data);
          if (data.data.status === 'COMPLETED') {
            setCurrentView('results');
          } else {
            setCurrentView('session');
          }
        }
      }
    } catch (error) {
      console.error('Error loading session:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewProgress = () => {
    setCurrentView('progress');
  };

  const handleRetrySession = () => {
    if (currentSession) {
      setCurrentView('configuration');
    }
  };

  const handleExportResults = () => {
    // TODO: Implement export functionality
    console.log('Export results for session:', currentSession?.id);
  };

  const handleBackToDashboard = () => {
    setCurrentView('dashboard');
    setCurrentSession(null);
  };

  const getSessionStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'IN_PROGRESS':
        return <Play className="h-4 w-4 text-blue-600" />;
      case 'PAUSED':
        return <Pause className="h-4 w-4 text-yellow-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getSessionStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'PAUSED':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const formatSessionType = (sessionType: string) => {
    const typeMap: Record<string, string> = {
      'QUICK_PRACTICE': 'Quick Practice',
      'FOCUSED_SESSION': 'Focused Session',
      'MOCK_INTERVIEW': 'Mock Interview',
      'BEHAVIORAL_PRACTICE': 'Behavioral Practice',
      'TECHNICAL_PRACTICE': 'Technical Practice',
      'CUSTOM_SESSION': 'Custom Session',
    };
    return typeMap[sessionType] || sessionType.replace(/_/g, ' ');
  };

  const formatSessionStatus = (status: string) => {
    const statusMap: Record<string, string> = {
      'NOT_STARTED': 'Not Started',
      'IN_PROGRESS': 'In Progress',
      'PAUSED': 'Paused',
      'COMPLETED': 'Completed',
      'ABANDONED': 'Abandoned',
    };
    return statusMap[status] || status.replace(/_/g, ' ');
  };

  const formatDuration = (timeValue: number) => {
    // Ensure we're working with a valid number and handle both seconds and minutes
    const validTime = Math.max(0, Math.floor(timeValue || 0));

    // If the value seems to be in seconds (very large number), convert to minutes
    const minutes = validTime > 3600 ? Math.floor(validTime / 60) : validTime;

    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (currentView === 'configuration') {
    return (
      <div className="container mx-auto px-4 py-8">
        <InterviewConfigurationWizard
          onConfigurationComplete={handleConfigurationComplete}
          onCancel={handleBackToDashboard}
        />
      </div>
    );
  }

  if (currentView === 'session') {
    if (!currentSession) {
      return (
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400 mb-4">Session not found or failed to load</p>
              <Button onClick={handleBackToDashboard}>Back to Dashboard</Button>
            </div>
          </div>
        </div>
      );
    }

    return (
      <InterviewSessionInterface
        session={currentSession}
        onSessionComplete={() => handleSessionComplete(currentSession.id)}
        onBackToDashboard={handleBackToDashboard}
      />
    );
  }

  if (currentView === 'results') {
    if (!currentSession) {
      return (
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400 mb-4">Results not found or failed to load</p>
              <Button onClick={handleBackToDashboard}>Back to Dashboard</Button>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="container mx-auto px-4 py-8">
        <InterviewResultsAndFeedback
          session={currentSession}
          onStartNewSession={handleStartNewSession}
          onRetrySession={handleRetrySession}
          onViewProgress={handleViewProgress}
          onExportResults={handleExportResults}
        />
      </div>
    );
  }

  if (currentView === 'progress') {
    return (
      <div className="container mx-auto px-4 py-8">
        <InterviewResultsDashboard
          onViewSession={handleViewSession}
          onStartNewSession={handleStartNewSession}
        />
      </div>
    );
  }

  // Show loading state while checking authentication or CSRF token
  if (status === 'loading' || csrfLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-gray-100 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show landing page for unauthenticated users
  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto text-center space-y-8">
          {/* Hero Section */}
          <div className="space-y-4">
            <h1 className="text-4xl font-bold">Interview Practice</h1>
            <p className="text-xl text-gray-600 dark:text-gray-400">
              Practice common interview questions and improve your skills with AI-powered feedback
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            <Card>
              <CardContent className="p-6 text-center">
                <Target className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">AI-Generated Questions</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Get personalized interview questions based on your career path and experience level
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <BarChart3 className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Real-time Feedback</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Receive detailed analysis and scoring of your responses with improvement suggestions
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <TrendingUp className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Progress Tracking</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Monitor your improvement over time with comprehensive analytics and metrics
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Call to Action */}
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 mt-12">
            <h2 className="text-2xl font-bold mb-4">Ready to improve your interview skills?</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Sign up for free to start practicing with AI-powered interview questions and get personalized feedback
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" onClick={() => window.location.href = '/signup'}>
                Sign Up Free
              </Button>
              <Button variant="outline" size="lg" onClick={() => window.location.href = '/login'}>
                Sign In
              </Button>
            </div>
          </div>

          {/* Benefits */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12 text-left">
            <div>
              <h3 className="text-lg font-semibold mb-4">Practice Session Types</h3>
              <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Quick Practice (15-20 mins)
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Focused Sessions (30-45 mins)
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Mock Interviews (60-90 mins)
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Behavioral & Technical Practice
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">AI-Powered Features</h3>
              <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Personalized question generation
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Response analysis & scoring
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Improvement recommendations
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Progress tracking & analytics
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Interview Practice</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Practice common interview questions and improve your skills
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleViewProgress}>
            <BarChart3 className="h-5 w-5 mr-2" />
            View Progress
          </Button>
          <Button onClick={handleStartNewSession} size="lg">
            <Plus className="h-5 w-5 mr-2" />
            Start New Practice
          </Button>
        </div>
      </div>

      {error && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {isLoading ? (
          Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))
        ) : progressStats ? (
          <>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Sessions
                  </span>
                </div>
                <div className="mt-2">
                  <div className="text-2xl font-bold">{progressStats.totalSessions}</div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {progressStats.completedSessions} completed
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Average Score
                  </span>
                </div>
                <div className="mt-2">
                  <div className="text-2xl font-bold">
                    {progressStats.averageScore ? `${progressStats.averageScore.toFixed(1)}/10` : 'N/A'}
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {progressStats.improvementRate !== null && progressStats.improvementRate !== undefined
                      ? `${progressStats.improvementRate > 0 ? '+' : ''}${progressStats.improvementRate.toFixed(1)}% trend`
                      : 'No trend data'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Clock className="h-5 w-5 text-purple-600" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Practice Time
                  </span>
                </div>
                <div className="mt-2">
                  <div className="text-2xl font-bold">
                    {formatDuration(progressStats.totalPracticeTime)}
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Total time invested
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Award className="h-5 w-5 text-orange-600" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Current Streak
                  </span>
                </div>
                <div className="mt-2">
                  <div className="text-2xl font-bold">{progressStats.currentStreak}</div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Days practicing
                  </p>
                </div>
              </CardContent>
            </Card>
          </>
        ) : (
          <Card className="col-span-full">
            <CardContent className="p-6 text-center">
              <p className="text-gray-600 dark:text-gray-400">
                Start your first practice session to see your progress stats
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Recent Sessions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Practice Sessions</CardTitle>
              <CardDescription>
                Your latest interview practice sessions
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={handleViewProgress}>
              <BarChart3 className="h-4 w-4 mr-2" />
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-32 mb-2" />
                    <Skeleton className="h-3 w-48" />
                  </div>
                  <Skeleton className="h-8 w-20" />
                </div>
              ))}
            </div>
          ) : sessions.length > 0 ? (
            <div className="space-y-4">
              {sessions.map((session) => (
                <div
                  key={session.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      {getSessionStatusIcon(session.status)}
                      <Badge className={getSessionStatusColor(session.status)}>
                        {formatSessionStatus(session.status)}
                      </Badge>
                    </div>
                    <div>
                      <h4 className="font-medium">
                        {formatSessionType(session.sessionType)}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {session.careerPath && `${session.careerPath} • `}
                        {session.completedQuestions}/{session.totalQuestions} questions
                        {session.overallScore && ` • Score: ${session.overallScore.toFixed(1)}/10`}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500">
                        {session.lastActiveAt ? formatDate(session.lastActiveAt) : formatDate(session.createdAt)} • {formatDuration(session.timeSpent)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {session.status === 'IN_PROGRESS' || session.status === 'PAUSED' ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleResumeSession(session.id)}
                      >
                        <Play className="h-4 w-4 mr-2" />
                        Resume
                      </Button>
                    ) : (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewSession(session.id)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Results
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No practice sessions yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Start your first interview practice session to begin improving your skills
              </p>
              <Button onClick={handleStartNewSession}>
                <Plus className="h-4 w-4 mr-2" />
                Start Your First Practice
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
