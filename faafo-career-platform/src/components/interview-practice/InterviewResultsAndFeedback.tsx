'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Clock, 
  Star, 
  CheckCircle, 
  AlertCircle, 
  Lightbulb, 
  BookOpen, 
  BarChart3,
  MessageSquare,
  Award,
  RefreshCw,
  Download,
  Share2,
  Eye
} from 'lucide-react';

interface InterviewResponse {
  id: string;
  questionId: string;
  responseText: string;
  responseTime: number;
  preparationTime: number;
  aiScore?: number;
  aiAnalysis?: any;
  feedback?: any;
  strengths?: string[];
  improvements?: any[];
  starMethodScore?: number;
  confidenceLevel?: number;
  communicationScore?: number;
  technicalScore?: number;
  userNotes?: string;
  question: {
    questionText: string;
    questionType: string;
    category: string;
    difficulty: string;
    questionOrder: number;
  };
}

interface InterviewSession {
  id: string;
  sessionType: string;
  status: string;
  totalQuestions: number;
  completedQuestions: number;
  overallScore?: number;
  timeSpent: number;
  careerPath?: string;
  experienceLevel?: string;
  companyType?: string;
  aiInsights?: any;
  responses: InterviewResponse[];
  createdAt: string;
  completedAt?: string;
}

interface InterviewResultsAndFeedbackProps {
  session: InterviewSession;
  onStartNewSession: () => void;
  onRetrySession: () => void;
  onViewProgress: () => void;
  onExportResults: () => void;
}

export default function InterviewResultsAndFeedback({
  session,
  onStartNewSession,
  onRetrySession,
  onViewProgress,
  onExportResults,
}: InterviewResultsAndFeedbackProps) {
  const [selectedResponse, setSelectedResponse] = useState<string | null>(null);

  // Filter for valid responses with scores
  const completedResponses = session.responses.filter(r =>
    r.aiScore !== null &&
    r.aiScore !== undefined &&
    typeof r.aiScore === 'number' &&
    !isNaN(r.aiScore)
  );

  // Calculate average score with proper validation
  const averageScore = completedResponses.length > 0
    ? Math.round((completedResponses.reduce((sum, r) => sum + (r.aiScore || 0), 0) / completedResponses.length) * 10) / 10
    : 0;

  // Calculate average response time with validation
  const averageResponseTime = completedResponses.length > 0
    ? Math.round((completedResponses.reduce((sum, r) => sum + Math.max(0, r.responseTime || 0), 0) / completedResponses.length) * 10) / 10
    : 0;

  const categoryScores = completedResponses.reduce((acc, response) => {
    const category = response?.question?.category || 'General';
    const score = response.aiScore || 0;

    // Validate score is a valid number
    if (typeof score !== 'number' || isNaN(score)) {
      return acc;
    }

    if (!acc[category]) {
      acc[category] = { total: 0, count: 0, scores: [] };
    }
    acc[category].total += score;
    acc[category].count += 1;
    acc[category].scores.push(score);
    return acc;
  }, {} as Record<string, { total: number; count: number; scores: number[] }>);

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600 dark:text-green-400';
    if (score >= 6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 8) return 'default';
    if (score >= 6) return 'secondary';
    return 'destructive';
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const getPerformanceInsights = () => {
    const insights = [];
    
    if (averageScore >= 8) {
      insights.push({
        type: 'success',
        title: 'Excellent Performance',
        description: 'You demonstrated strong interview skills across multiple areas.',
      });
    } else if (averageScore >= 6) {
      insights.push({
        type: 'warning',
        title: 'Good Foundation',
        description: 'You have solid interview skills with room for targeted improvement.',
      });
    } else {
      insights.push({
        type: 'info',
        title: 'Growth Opportunity',
        description: 'Focus on fundamental interview techniques to build confidence.',
      });
    }

    // Category-specific insights
    Object.entries(categoryScores).forEach(([category, data]) => {
      const avgScore = data.total / data.count;
      if (avgScore < 6) {
        insights.push({
          type: 'improvement',
          title: `${category.replace('_', ' ')} Needs Attention`,
          description: `Consider practicing more questions in this area.`,
        });
      } else if (avgScore >= 8) {
        insights.push({
          type: 'strength',
          title: `Strong ${category.replace('_', ' ')} Skills`,
          description: `You excel in this area - leverage this strength.`,
        });
      }
    });

    return insights;
  };

  const insights = getPerformanceInsights();

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Award className="h-6 w-6 text-yellow-500" />
                <span>Interview Practice Results</span>
              </CardTitle>
              <CardDescription>
                Session completed on {new Date(session.completedAt || session.createdAt).toLocaleDateString()}
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={onExportResults}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Overall Score</p>
                <p className={`text-2xl font-bold ${getScoreColor(averageScore)}`}>
                  {averageScore.toFixed(1)}/10
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Questions Completed</p>
                <p className="text-2xl font-bold">
                  {session.completedQuestions}/{session.totalQuestions}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Time</p>
                <p className="text-2xl font-bold">
                  {formatDuration(session.timeSpent)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Response Time</p>
                <p className="text-2xl font-bold">
                  {formatTime(Math.round(averageResponseTime))}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance by Category */}
      <Card>
        <CardHeader>
          <CardTitle>Performance by Category</CardTitle>
          <CardDescription>
            Your scores across different question categories
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(categoryScores).map(([category, data]) => {
              const avgScore = data.total / data.count;
              const percentage = (avgScore / 10) * 100;
              
              return (
                <div key={category} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      {category.replace('_', ' ')}
                    </span>
                    <Badge variant={getScoreBadgeVariant(avgScore)}>
                      {avgScore.toFixed(1)}/10
                    </Badge>
                  </div>
                  <Progress value={percentage} className="h-2" />
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {data.count} question{data.count !== 1 ? 's' : ''}
                  </p>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Feedback */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Feedback</CardTitle>
          <CardDescription>
            AI analysis and suggestions for each response
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {session.responses.filter(r => r.feedback).map((response) => (
              <div key={response.id} className="space-y-4 p-4 border rounded-lg">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">Q{response.question.questionOrder}</Badge>
                  <h4 className="font-medium">{response.question.questionText}</h4>
                  {response.aiScore && (
                    <Badge variant={getScoreBadgeVariant(response.aiScore)}>
                      {response.aiScore.toFixed(1)}/10
                    </Badge>
                  )}
                </div>

                {response.feedback && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {response.feedback.positive && response.feedback.positive.length > 0 && (
                      <Alert>
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>
                          <strong>Strengths:</strong>
                          <ul className="list-disc list-inside mt-2">
                            {response.feedback.positive.map((item: string, index: number) => (
                              <li key={index}>{item}</li>
                            ))}
                          </ul>
                        </AlertDescription>
                      </Alert>
                    )}

                    {response.feedback.constructive && response.feedback.constructive.length > 0 && (
                      <Alert>
                        <Lightbulb className="h-4 w-4" />
                        <AlertDescription>
                          <strong>Areas for Improvement:</strong>
                          <ul className="list-disc list-inside mt-2">
                            {response.feedback.constructive.map((item: string, index: number) => (
                              <li key={index}>{item}</li>
                            ))}
                          </ul>
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                )}

                {response.improvements && response.improvements.length > 0 && (
                  <div>
                    <h5 className="font-medium mb-2">Specific Suggestions:</h5>
                    <div className="space-y-2">
                      {response.improvements.map((improvement: any, index: number) => (
                        <div key={index} className="bg-blue-50 dark:bg-blue-950 p-3 rounded">
                          <p className="font-medium text-sm">{improvement.area}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {improvement.suggestion}
                          </p>
                          <Badge variant="outline" className="mt-1 text-xs">
                            {improvement.priority} priority
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Next Steps */}
      <Card>
        <CardHeader>
          <CardTitle>Next Steps</CardTitle>
          <CardDescription>
            Recommended actions to improve your interview skills
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button onClick={onStartNewSession} className="h-auto p-4">
              <div className="text-left">
                <div className="flex items-center space-x-2 mb-1">
                  <RefreshCw className="h-4 w-4" />
                  <span className="font-medium">Practice More</span>
                </div>
                <p className="text-xs opacity-80">
                  Start a new practice session to build on your progress
                </p>
              </div>
            </Button>

            <Button variant="outline" onClick={onViewProgress} className="h-auto p-4">
              <div className="text-left">
                <div className="flex items-center space-x-2 mb-1">
                  <BarChart3 className="h-4 w-4" />
                  <span className="font-medium">View Progress</span>
                </div>
                <p className="text-xs opacity-80">
                  See your improvement over time and track goals
                </p>
              </div>
            </Button>

            <Button variant="outline" onClick={onRetrySession} className="h-auto p-4">
              <div className="text-left">
                <div className="flex items-center space-x-2 mb-1">
                  <Target className="h-4 w-4" />
                  <span className="font-medium">Retry Session</span>
                </div>
                <p className="text-xs opacity-80">
                  Practice the same questions with improved responses
                </p>
              </div>
            </Button>

            <Button variant="outline" className="h-auto p-4">
              <div className="text-left">
                <div className="flex items-center space-x-2 mb-1">
                  <BookOpen className="h-4 w-4" />
                  <span className="font-medium">Study Resources</span>
                </div>
                <p className="text-xs opacity-80">
                  Find learning materials for your improvement areas
                </p>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
