import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { with<PERSON>rror<PERSON>andler } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { z } from 'zod';

// Validation schema for creating interview sessions
const createSessionSchema = z.object({
  sessionType: z.enum(['QUICK_PRACTICE', 'FOCUSED_SESSION', 'MOCK_INTERVIEW', 'BEHAVIORAL_PRACTICE', 'TECHNICAL_PRACTICE', 'CUSTOM_SESSION']),
  careerPath: z.string().optional(),
  experienceLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  companyType: z.string().optional(),
  industryFocus: z.string().optional(),
  specificRole: z.string().optional(),
  interviewType: z.enum(['PHONE', 'VIDEO', 'IN_PERSON', 'PANEL', 'GROUP', 'TECHNICAL_SCREEN', 'BEHAVIORAL', 'CASE_STUDY']).optional(),
  preparationTime: z.string().optional(),
  focusAreas: z.array(z.string()).optional(),
  difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).default('BEGINNER'),
  totalQuestions: z.number().min(1).max(50).default(10),
});

// GET - Retrieve user's interview sessions
export async function GET(request: NextRequest) {
  return withRateLimit(
    request,
    {
      windowMs: 15 * 60 * 1000,
      maxRequests: process.env.NODE_ENV === 'development' ? 100 : 30 // Higher limit for development
    },
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { searchParams } = new URL(request.url);
      const status = searchParams.get('status');
      const limit = parseInt(searchParams.get('limit') || '10');
      const offset = parseInt(searchParams.get('offset') || '0');

      try {
        const whereClause: any = { userId };
        if (status) {
          whereClause.status = status;
        }

        const [sessions, total] = await Promise.all([
          prisma.interviewSession.findMany({
            where: whereClause,
            include: {
              questions: {
                select: {
                  id: true,
                  questionType: true,
                  category: true,
                  difficulty: true,
                  questionOrder: true,
                },
                orderBy: { questionOrder: 'asc' }
              },
              responses: {
                where: { userId }, // Filter responses by current user
                select: {
                  id: true,
                  questionId: true,
                  isCompleted: true,
                  aiScore: true,
                },
              },
              _count: {
                select: {
                  questions: true,
                  responses: {
                    where: { userId, isCompleted: true }
                  }
                }
              }
            },
            orderBy: { createdAt: 'desc' },
            take: limit,
            skip: offset,
          }),
          prisma.interviewSession.count({ where: whereClause }),
        ]);

        return NextResponse.json({
          success: true,
          data: {
            sessions,
            pagination: {
              total,
              limit,
              offset,
              hasMore: offset + limit < total,
            },
          },
        });
      } catch (error) {
        console.error('Error fetching interview sessions:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to fetch interview sessions' },
          { status: 500 }
        );
      }
    }
  );
}

// POST - Create new interview session
export const POST = withErrorHandler(async (request: NextRequest) => {
  // Temporarily disable CSRF protection for development
  const skipCSRF = process.env.NODE_ENV === 'development';

  const handler = async () => {
    return withRateLimit(
      request,
      {
        windowMs: 15 * 60 * 1000,
        maxRequests: process.env.NODE_ENV === 'development' ? 50 : 10 // Higher limit for development
      },
      async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;

      try {
        const body = await request.json();
        const validation = createSessionSchema.safeParse(body);
        
        if (!validation.success) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Invalid request data',
              details: validation.error.errors 
            },
            { status: 400 }
          );
        }

        const sessionData = validation.data;

        // Create the interview session
        const interviewSession = await prisma.interviewSession.create({
          data: {
            userId,
            sessionType: sessionData.sessionType,
            careerPath: sessionData.careerPath,
            experienceLevel: sessionData.experienceLevel,
            companyType: sessionData.companyType,
            industryFocus: sessionData.industryFocus,
            specificRole: sessionData.specificRole,
            interviewType: sessionData.interviewType,
            preparationTime: sessionData.preparationTime,
            focusAreas: sessionData.focusAreas,
            difficulty: sessionData.difficulty,
            totalQuestions: sessionData.totalQuestions,
            status: 'IN_PROGRESS',
            sessionConfig: {
              createdVia: 'api',
              userAgent: request.headers.get('user-agent'),
            },
          },
          select: {
            id: true,
            userId: true,
            sessionType: true,
            careerPath: true,
            experienceLevel: true,
            companyType: true,
            industryFocus: true,
            specificRole: true,
            interviewType: true,
            preparationTime: true,
            focusAreas: true,
            difficulty: true,
            totalQuestions: true,
            status: true,
            timeSpent: true,
            startedAt: true,
            createdAt: true,
            updatedAt: true,
            _count: {
              select: {
                questions: true,
                responses: true
              }
            }
          },
        });

        // Automatically generate questions for the session
        try {
          const questionsResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/api/interview-practice/${interviewSession.id}/questions`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cookie': request.headers.get('cookie') || '',
            },
            body: JSON.stringify({
              count: sessionData.totalQuestions,
            }),
          });

          if (!questionsResponse.ok) {
            console.warn('Failed to auto-generate questions for session:', interviewSession.id);
          }
        } catch (error) {
          console.warn('Error auto-generating questions:', error);
          // Don't fail session creation if question generation fails
        }

        return NextResponse.json({
          success: true,
          data: interviewSession,
          message: 'Interview session created successfully',
        });
      } catch (error) {
        console.error('Error creating interview session:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to create interview session' },
          { status: 500 }
        );
      }
    }
    );
  };

  if (skipCSRF) {
    return handler();
  } else {
    return withCSRFProtection(request, handler);
  }
});
